import { Response } from 'express';
import { ProjectService, CreateProjectRequest, UpdateProjectRequest } from '../services/ProjectService';
import { ThreadService } from '../services/ThreadService';
import { ResponseUtil } from '../utils/response';
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';
import { AuthenticatedRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../config/logger';
import { FileProcessingService, FileAttachment } from '../services/FileProcessingService';

export class ProjectController {
  /**
   * Create a new project
   */
  static createProject = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const projectData: CreateProjectRequest = req.body;
    const project = await ProjectService.createProject(req.user.userId, projectData);
    
    ResponseUtil.success(res, 'Project created successfully', project);
  });

  /**
   * Get user projects
   */
  static getUserProjects = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit as string) || 6;
    const offset = parseInt(req.query.offset as string) || 0;

    const projects = await ProjectService.getUserProjects(req.user.userId, limit, offset);
    
    ResponseUtil.success(res, 'Projects retrieved successfully', {
      projects,
      limit,
      offset,
    });
  });

  /**
   * Get project by ID
   */
  static getProject = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    const project = await ProjectService.getProject(projectId, req.user.userId);
    
    if (!project) {
      ResponseUtil.notFound(res, 'Project not found');
      return;
    }

    // Get project statistics
    const stats = await ProjectService.getProjectStats(projectId, req.user.userId);

    const projectData = {
      ...project.toJSON(),
      stats,
    };

    ResponseUtil.success(res, 'Project retrieved successfully', projectData);
  });

  /**
   * Update project
   */
  static updateProject = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;
    const updateData: UpdateProjectRequest = req.body;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    const project = await ProjectService.updateProject(projectId, req.user.userId, updateData);
    
    ResponseUtil.success(res, 'Project updated successfully', project);
  });

  /**
   * Delete project
   */
  static deleteProject = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    await ProjectService.deleteProject(projectId, req.user.userId);
    
    ResponseUtil.success(res, 'Project deleted successfully');
  });

  /**
   * Get project threads
   */
  static getProjectThreads = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;
    const limit = parseInt(req.query.limit as string) || 6;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    const threads = await ProjectService.getProjectThreads(
      projectId,
      req.user.userId,
      limit,
      offset
    );
    
    ResponseUtil.success(res, 'Project threads retrieved successfully', {
      threads,
      projectId,
      limit,
      offset,
    });
  });

  /**
   * Send message in project thread
   */
  static sendProjectMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;
    const { message, threadId, llmModel } = req.body;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    if (!message || message.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Message is required');
      return;
    }

    // Verify project exists and belongs to user
    const project = await ProjectService.getProject(projectId, req.user.userId);
    if (!project) {
      ResponseUtil.notFound(res, 'Project not found');
      return;
    }

    const chatRequest = {
      message,
      threadId,
      projectId,
      llmModel,
    };

    const result = await ThreadService.processThreadChat(req.user.userId, chatRequest);
    
    ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
  });

  /**
   * Send streaming message in project thread
   */
  static sendProjectStreamingMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;
    const { message, threadId, llmModel } = req.body;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    if (!message || message.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Message is required');
      return;
    }

    try {
      // Verify project exists and belongs to user
      const project = await ProjectService.getProject(projectId, req.user.userId);
      if (!project) {
        ResponseUtil.notFound(res, 'Project not found');
        return;
      }

      const chatRequest = {
        message,
        threadId,
        projectId,
        llmModel,
      };

      const result = await ThreadService.processThreadChatStreaming(req.user.userId, chatRequest, res);
      // Note: Response is handled by the streaming service, no need to send additional response
    } catch (error) {
      logger.error('Error in streaming project chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Search projects
   */
  static searchProjects = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { q: searchTerm } = req.query;
    const limit = parseInt(req.query.limit as string) || 6;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!searchTerm || typeof searchTerm !== 'string' || searchTerm.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Search term is required');
      return;
    }

    const projects = await ProjectService.searchProjects(
      req.user.userId,
      searchTerm.trim(),
      limit,
      offset
    );
    
    ResponseUtil.success(res, 'Projects search completed', {
      projects,
      searchTerm: searchTerm.trim(),
      limit,
      offset,
    });
  });

  /**
   * Get project statistics
   */
  static getProjectStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    const stats = await ProjectService.getProjectStats(projectId, req.user.userId);

    ResponseUtil.success(res, 'Project statistics retrieved successfully', stats);
  });

  /**
   * Send streaming message in project thread with file attachment support
   */
  static sendProjectStreamingMessageWithAttachment = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { projectId } = req.params;
    const { message, threadId, llmModel } = req.body;
    const file = req.file;

    if (!projectId) {
      ResponseUtil.badRequest(res, 'Project ID is required');
      return;
    }

    if (!message || message.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Message is required');
      return;
    }

    try {
      // Verify project exists and belongs to user
      const project = await ProjectService.getProject(projectId, req.user.userId);
      if (!project) {
        ResponseUtil.notFound(res, 'Project not found');
        return;
      }

      let processedFile = undefined;

      // Process file if attached
      if (file) {
        const fileAttachment: FileAttachment = {
          buffer: file.buffer,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        };

        processedFile = await FileProcessingService.processFile(fileAttachment);
        logger.info(`File processed successfully: ${file.originalname} (${file.mimetype})`);
      }

      const chatRequest = {
        message,
        threadId,
        projectId,
        llmModel,
      };

      const result = await ThreadService.processThreadChatStreamingWithAttachment(
        req.user.userId,
        chatRequest,
        res,
        processedFile
      );
      // Note: Response is handled by the streaming service, no need to send additional response
    } catch (error) {
      logger.error('Error in streaming project chat with attachment:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });
}
